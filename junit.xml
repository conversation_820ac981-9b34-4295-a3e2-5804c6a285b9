<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="7" failures="5" errors="0" time="6.212">
  <testsuite name="WhatsApp Webhook Integration" errors="0" failures="1" skipped="0" timestamp="2025-06-07T10:53:55" time="4.522" tests="3">
    <testcase classname="WhatsApp Webhook Integration should process incoming message and create session" name="WhatsApp Webhook Integration should process incoming message and create session" time="0.102">
      <failure>TypeError: Cannot read properties of null (reading &apos;ObjectId&apos;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/objectid.js:13:44)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/utils.js:10:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/statemachine.js:8:15)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/internal.js:7:22)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:8:23)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/subdocument.js:3:18)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/types/ArraySubdocument.js:8:21)
    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)
    at Runtime.requireModuleOrMock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1048:21)
    at model.Object.&lt;anonymous&gt;.Document.$__set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1666:26)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1495:10)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1151:16)
    at model.Document (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:163:12)
    at model.Model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:124:12)
    at new model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:5239:15)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3213:22
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3248:7
    at Array.forEach (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3247:15
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Function.create (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3178:23)
    at create (/home/<USER>/firespoon/Firespoon_API_TF/test/factories/userFactory.js:48:23)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Function.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Function.apply [as create] (/home/<USER>/firespoon/Firespoon_API_TF/test/factories/userFactory.js:46:22)
    at create (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:48:36)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="WhatsApp Webhook Integration should reject webhook with invalid signature" name="WhatsApp Webhook Integration should reject webhook with invalid signature" time="1.677">
    </testcase>
    <testcase classname="WhatsApp Webhook Integration should handle malformed webhook payload" name="WhatsApp Webhook Integration should handle malformed webhook payload" time="0.009">
    </testcase>
  </testsuite>
  <testsuite name="Order GraphQL Mutations" errors="0" failures="4" skipped="0" timestamp="2025-06-07T10:54:00" time="1.422" tests="4">
    <testcase classname="Order GraphQL Mutations should create a new order" name="Order GraphQL Mutations should create a new order" time="0.168">
      <failure>ValidationError: Restaurant validation failed: address: Cast to string failed for value &quot;{
  street: &apos;186 Reanna Expressway&apos;,
  city: &apos;Faustinochester&apos;,
  state: &apos;Montana&apos;,
  zipCode: &apos;77007-1805&apos;,
  country: &apos;Dominican Republic&apos;,
  location: { type: &apos;Point&apos;, coordinates: [ 121.062, -45.805 ] }
}&quot; (type Object) at path &quot;address&quot;, categories: Cast to embedded failed for value &quot;Chinese&quot; (type string) at path &quot;categories&quot; because of &quot;ObjectParameterError&quot;
    at model.Object.&lt;anonymous&gt;.Document.invalidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3197:32)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1473:12)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1151:16)
    at model.Document (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:163:12)
    at model.Model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:124:12)
    at new model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:5239:15)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3213:22
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3248:7
    at Array.forEach (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3247:15
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Function.create (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3178:23)
    at create (/home/<USER>/firespoon/Firespoon_API_TF/test/factories/restaurantFactory.js:113:29)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Function.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Function.apply [as create] (/home/<USER>/firespoon/Firespoon_API_TF/test/factories/restaurantFactory.js:111:22)
    at create (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:29:42)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</failure>
    </testcase>
    <testcase classname="Order GraphQL Mutations should fail to create order with invalid restaurant ID" name="Order GraphQL Mutations should fail to create order with invalid restaurant ID" time="0.14">
      <failure>ValidationError: Restaurant validation failed: address: Cast to string failed for value &quot;{
  street: &apos;77493 Hallie Extension&apos;,
  city: &apos;St. Cloud&apos;,
  state: &apos;Delaware&apos;,
  zipCode: &apos;34180-3790&apos;,
  country: &apos;Holy See (Vatican City State)&apos;,
  location: { type: &apos;Point&apos;, coordinates: [ -31.6831, 83.1442 ] }
}&quot; (type Object) at path &quot;address&quot;, categories: Cast to embedded failed for value &quot;Mexican&quot; (type string) at path &quot;categories&quot; because of &quot;ObjectParameterError&quot;
    at model.Object.&lt;anonymous&gt;.Document.invalidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3197:32)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1473:12)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1151:16)
    at model.Document (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:163:12)
    at model.Model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:124:12)
    at new model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:5239:15)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3213:22
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3248:7
    at Array.forEach (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3247:15
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Function.create (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3178:23)
    at create (/home/<USER>/firespoon/Firespoon_API_TF/test/factories/restaurantFactory.js:113:29)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Function.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Function.apply [as create] (/home/<USER>/firespoon/Firespoon_API_TF/test/factories/restaurantFactory.js:111:22)
    at create (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:29:42)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</failure>
    </testcase>
    <testcase classname="Order GraphQL Mutations should fail to create order without authentication" name="Order GraphQL Mutations should fail to create order without authentication" time="0.206">
      <failure>ValidationError: Restaurant validation failed: address: Cast to string failed for value &quot;{
  street: &apos;384 W Pine Street&apos;,
  city: &apos;South Letha&apos;,
  state: &apos;Alabama&apos;,
  zipCode: &apos;54979-5598&apos;,
  country: &apos;Zambia&apos;,
  location: { type: &apos;Point&apos;, coordinates: [ -4.1381, 59.6343 ] }
}&quot; (type Object) at path &quot;address&quot;, categories: Cast to embedded failed for value &quot;Italian&quot; (type string) at path &quot;categories&quot; because of &quot;ObjectParameterError&quot;
    at model.Object.&lt;anonymous&gt;.Document.invalidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3197:32)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1473:12)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1151:16)
    at model.Document (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:163:12)
    at model.Model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:124:12)
    at new model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:5239:15)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3213:22
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3248:7
    at Array.forEach (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3247:15
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Function.create (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3178:23)
    at create (/home/<USER>/firespoon/Firespoon_API_TF/test/factories/restaurantFactory.js:113:29)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Function.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Function.apply [as create] (/home/<USER>/firespoon/Firespoon_API_TF/test/factories/restaurantFactory.js:111:22)
    at create (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:29:42)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</failure>
    </testcase>
    <testcase classname="Order GraphQL Mutations should update order status" name="Order GraphQL Mutations should update order status" time="0.153">
      <failure>ValidationError: Restaurant validation failed: address: Cast to string failed for value &quot;{
  street: &apos;7327 Modesto Mews&apos;,
  city: &apos;West Aimeeworth&apos;,
  state: &apos;North Dakota&apos;,
  zipCode: &apos;80257&apos;,
  country: &apos;Malta&apos;,
  location: { type: &apos;Point&apos;, coordinates: [ 18.3273, 6.5081 ] }
}&quot; (type Object) at path &quot;address&quot;, categories: Cast to embedded failed for value &quot;Japanese&quot; (type string) at path &quot;categories&quot; because of &quot;ObjectParameterError&quot;
    at model.Object.&lt;anonymous&gt;.Document.invalidate (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:3197:32)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1473:12)
    at model.$set (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:1151:16)
    at model.Document (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/document.js:163:12)
    at model.Model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:124:12)
    at new model (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:5239:15)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3213:22
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3248:7
    at Array.forEach (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3247:15
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Function.create (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/model.js:3178:23)
    at create (/home/<USER>/firespoon/Firespoon_API_TF/test/factories/restaurantFactory.js:113:29)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Function.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Function.apply [as create] (/home/<USER>/firespoon/Firespoon_API_TF/test/factories/restaurantFactory.js:111:22)
    at create (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:29:42)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</failure>
    </testcase>
  </testsuite>
</testsuites>