# Firespoon API Testing Framework

This directory contains the testing framework for the Firespoon API. The framework is built on Jest and provides utilities for testing all aspects of the application, including GraphQL API, WhatsApp integration, and payment processing.

## Table of Contents

- [Getting Started](#getting-started)
- [Test Structure](#test-structure)
- [Running Tests](#running-tests)
- [Writing Tests](#writing-tests)
- [Test Helpers](#test-helpers)
- [Test Factories](#test-factories)
- [Fixtures](#fixtures)
- [Mocking External Services](#mocking-external-services)
- [Debugging Tests](#debugging-tests)

## Getting Started

To get started with testing, make sure you have installed all the required dependencies:

```bash
npm install
```

### Database Setup

The testing framework uses:

1. **MongoDB Atlas 8.0.8** for the database
   - Create a `.env.test` file based on `.env.test.example`
   - Set `CONNECTION_STRING` to your MongoDB Atlas connection string
   - This matches your production environment

2. **Redis 7.2.4** via local Docker container
   - Make sure Docker is installed and running
   - The framework will automatically create a Redis container

## Test Structure

The test directory is organized as follows:

```
test/
├── config/                # Test configuration
│   ├── jest.config.js     # Jest configuration
│   ├── globalSetup.js     # Test environment setup
│   └── globalTeardown.js  # Test environment cleanup
├── fixtures/              # Static test data
│   ├── whatsapp/          # WhatsApp webhook payloads
│   └── stripe/            # Stripe webhook payloads
├── factories/             # Test data factories
│   ├── userFactory.js
│   └── restaurantFactory.js
├── helpers/               # Test utilities
│   ├── apiHelper.js       # API request helpers
│   ├── dbHelper.js        # Database operations
│   ├── redisHelper.js     # Redis operations
│   ├── authHelper.js      # Authentication utilities
│   ├── mockHelper.js      # External API mocking
│   └── waitFor.js         # Async utilities
├── unit/                  # Unit tests
│   ├── services/
│   ├── models/
│   └── utils/
├── integration/           # Integration tests
│   ├── graphql/
│   ├── rest/
│   └── whatsapp/
└── e2e/                   # End-to-end tests
```

## Running Tests

The following npm scripts are available for running tests:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration

# Run only end-to-end tests
npm run test:e2e
```

## Writing Tests

### Unit Tests

Unit tests focus on testing individual functions, classes, or modules in isolation. External dependencies should be mocked.

Example:

```javascript
// test/unit/services/orderService.test.js
const orderService = require('../../../services/orderService');
const Order = require('../../../models/order');

// Mock the Order model
jest.mock('../../../models/order');

describe('Order Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should calculate order total correctly', () => {
    // Test implementation
  });
});
```

### Integration Tests

Integration tests verify that different parts of the application work together correctly. These tests use the real test database and Redis, but mock external services.

Example:

```javascript
// test/integration/graphql/orderMutations.test.js
const { connectTestDB, clearCollections } = require('../../helpers/dbHelper');
const { generateAuthToken } = require('../../helpers/authHelper');
const { graphqlMutation } = require('../../helpers/apiHelper');
const UserFactory = require('../../factories/userFactory');

describe('Order GraphQL Mutations', () => {
  beforeAll(async () => {
    await connectTestDB();
  });

  beforeEach(async () => {
    await clearCollections('User', 'Restaurant', 'Order');
  });

  test('should create a new order', async () => {
    // Test implementation
  });
});
```

### End-to-End Tests

End-to-end tests simulate real user scenarios and test the entire application flow. These tests should be focused on critical user journeys.

Example:

```javascript
// test/e2e/orderFlow.test.js
const { connectTestDB, clearCollections } = require('../helpers/dbHelper');
const { generateAuthToken } = require('../helpers/authHelper');
const { graphqlMutation } = require('../helpers/apiHelper');
const { mockStripeCheckoutCreate, mockWhatsAppSend } = require('../helpers/mockHelper');

describe('Order Flow', () => {
  beforeAll(async () => {
    await connectTestDB();
  });

  beforeEach(async () => {
    await clearCollections('User', 'Restaurant', 'Order');
  });

  test('should complete full order flow from creation to delivery', async () => {
    // Test implementation
  });
});
```

## Test Helpers

The test framework provides several helper modules to simplify testing:

### Database Helper (`dbHelper.js`)

Provides functions for database operations with MongoDB Atlas:

```javascript
const { connectTestDB, clearCollections, findById, create } = require('../helpers/dbHelper');

// Connect to MongoDB Atlas 8.0.8
await connectTestDB();

// Clear specific collections (only clears test data to avoid affecting production data)
await clearCollections('User', 'Restaurant');

// Find a document by ID
const user = await findById('User', userId);

// Create a document (automatically marks the document as test data)
const newUser = await create('User', { name: 'Test User', email: '<EMAIL>' });

// Create a document without test markers (use with caution)
const productionLikeUser = await create('User', userData, false);
```

### Redis Helper (`redisHelper.js`)

Provides functions for Redis operations:

```javascript
const { getRedisClient, clearRedis, getSession } = require('../helpers/redisHelper');

// Clear Redis
await clearRedis();

// Get a session
const session = await getSession(sessionId);
```

### API Helper (`apiHelper.js`)

Provides functions for API requests:

```javascript
const { graphqlQuery, whatsappWebhook } = require('../helpers/apiHelper');

// Send a GraphQL query
const response = await graphqlQuery(query, variables, token);

// Send a WhatsApp webhook request
const response = await whatsappWebhook(payload, signature);
```

### Authentication Helper (`authHelper.js`)

Provides functions for authentication:

```javascript
const { generateAuthToken, generateXMMCSignature } = require('../helpers/authHelper');

// Generate a JWT token
const token = generateAuthToken(user);

// Generate a WhatsApp webhook signature
const signature = generateXMMCSignature(payload, secret);
```

### Mock Helper (`mockHelper.js`)

Provides functions for mocking external services:

```javascript
const { mockWhatsAppSend, mockStripeCheckoutCreate } = require('../helpers/mockHelper');

// Mock WhatsApp API
mockWhatsAppSend();

// Mock Stripe API
mockStripeCheckoutCreate();
```

### Async Helper (`waitFor.js`)

Provides functions for handling asynchronous operations:

```javascript
const { delay, waitForDbRecord, waitForRedisKey } = require('../helpers/waitFor');

// Wait for a database record
const order = await waitForDbRecord('Order', { _id: orderId }, record => record.status === 'PAID');

// Wait for a Redis key
const session = await waitForRedisKey(sessionKey, value => value !== null);
```

## Test Factories

Test factories provide a convenient way to create test data:

```javascript
const UserFactory = require('../factories/userFactory');
const RestaurantFactory = require('../factories/restaurantFactory');

// Create a user
const user = await UserFactory.create();

// Create a restaurant
const restaurant = await RestaurantFactory.create({ owner: user._id });
```

## Fixtures

Fixtures provide static test data for webhooks and other external inputs:

```javascript
const messageReceivedPayload = require('../fixtures/whatsapp/message-received.json');
const checkoutSessionCompletedPayload = require('../fixtures/stripe/checkout-session-completed.json');
```

## Mocking External Services

External services like WhatsApp and Stripe should be mocked in tests:

```javascript
const { mockWhatsAppSend, mockStripeCheckoutCreate } = require('../helpers/mockHelper');

// Mock WhatsApp API
mockWhatsAppSend({
  data: { id: 'mock-message-id' }
});

// Mock Stripe API
mockStripeCheckoutCreate({
  id: 'mock-session-id',
  url: 'https://checkout.stripe.com/mock'
});
```

## Debugging Tests

To debug tests, you can use the following techniques:

1. **Run a single test file**:

```bash
npx jest test/integration/graphql/orderMutations.test.js
```

2. **Run a specific test**:

```bash
npx jest -t "should create a new order"
```

3. **Enable verbose output**:

```bash
npx jest --verbose
```

4. **Increase timeout for slow tests**:

```javascript
jest.setTimeout(30000); // 30 seconds
```

5. **Debug with Node.js inspector**:

```bash
node --inspect-brk node_modules/.bin/jest test/integration/graphql/orderMutations.test.js
```

Then open Chrome and navigate to `chrome://inspect` to attach the debugger.

## MongoDB Atlas Configuration

### Setting Up MongoDB Atlas 8.0.8 for Testing

1. **Create a Test Database**:
   - Create a dedicated database for testing in your Atlas cluster
   - Use a clear naming convention (e.g., `firespoon_test`)

2. **Create a Test User**:
   - Create a dedicated database user with read/write permissions to the test database
   - Use a strong password and store it securely

3. **Configure Network Access**:
   - Add your development machine's IP address to the Atlas network access list
   - For CI/CD environments, you may need to allow access from those IP ranges

4. **Configure Connection String**:
   - Copy your connection string from Atlas
   - Replace the database name with your test database name
   - Add the connection string to your `.env.test` file:

```
CONNECTION_STRING=mongodb+srv://testuser:<EMAIL>/firespoon_test?retryWrites=true&w=majority
```

### Safety Features for Testing

The testing framework includes several safety features:

1. **Test Data Marking**:
   - All test data is automatically marked with `_testData: true`
   - Names and emails are prefixed with `test_`
   - This makes it easy to identify and clean up test data

2. **Selective Cleanup**:
   - `clearCollections()` and `clearDatabase()` only remove test-marked data
   - This prevents accidental deletion of production data
