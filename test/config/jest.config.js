/**
 * Jest configuration for Firespoon API testing
 */
const path = require('path');
module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Transform files with Babel
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // File extensions to consider
  moduleFileExtensions: ['js', 'json', 'node'],
  // 将 rootDir 设置为 jest.config.js 所在目录的父目录的父目录
  // 即 /home/<USER>/firespoon/Firespoon_API/
  rootDir: path.resolve(__dirname, '../..'), //  <-- 关键改动

  // Test file patterns
  testMatch: [
    '<rootDir>/test/**/*.test.js',
    '<rootDir>/test/**/*.spec.js'
  ],
  
  // Don't transform node_modules except for xstate
  transformIgnorePatterns: ['node_modules/(?!(xstate)/)'],
  
  
  // Setup files to run before tests
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  
  // Global setup and teardown
  globalSetup: '<rootDir>/test/config/globalSetup.js',
  globalTeardown: '<rootDir>/test/config/globalTeardown.js',
  
  // Run tests sequentially to avoid conflicts
  maxWorkers: 1,
  
  // Increase test timeout for integration tests
  testTimeout: 30000,
  
  // Force exit after tests to prevent hanging
  forceExit: true,
  
  // Detect memory leaks
  detectLeaks: true,
  
  // Reset modules between tests
  resetModules: true,
  
  // Coverage configuration
  collectCoverageFrom: [
    'graphql/**/*.js',
    'models/**/*.js',
    'routes/**/*.js',
    'services/**/*.js',
    'whatsapp/**/*.js',
    '!**/node_modules/**',
    '!**/test/**'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      statements: 70,
      branches: 60,
      functions: 70,
      lines: 70
    }
  },
  
  // Coverage reporters
  coverageReporters: ['text', 'lcov', 'clover', 'html'],
  
  // Test reporters
  reporters: ['default', 'jest-junit'],
  
  // Test result processor
  testResultsProcessor: 'jest-junit',
  
  // Watch plugins
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname'
  ]
};
