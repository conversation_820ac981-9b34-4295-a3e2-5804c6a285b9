/**
 * API helper functions for tests
 */

const request = require('supertest');

let app = null;
let server = null;
let httpServer = null;

/**
 * Initialize the app for testing
 * @returns {Promise<Object>} Express app instance
 */
async function initializeApp() {
  if (!app) {
    // Import and start the server for testing
    const { startApolloServer } = require('../../app');
    const serverInstance = await startApolloServer();
    app = serverInstance.app;
    server = serverInstance.server;
    httpServer = serverInstance.httpServer;
  }
  return app;
}

/**
 * Clean up the server instance
 * @returns {Promise<void>}
 */
async function cleanupApp() {
  if (server) {
    await server.stop();
    server = null;
  }
  if (httpServer) {
    await new Promise((resolve) => {
      httpServer.close(resolve);
    });
    httpServer = null;
  }
  app = null;
}

/**
 * Get a supertest request object for the app
 * @returns {Promise<Object>} Supertest request object
 */
async function getRequest() {
  const appInstance = await initializeApp();
  return request(appInstance);
}

/**
 * Send a GraphQL query
 * @param {string} query - GraphQL query or mutation
 * @param {Object} [variables={}] - Query variables
 * @param {string} [token=null] - Authentication token
 * @returns {Promise<Object>} Response
 */
async function graphqlQuery(query, variables = {}, token = null) {
  const requestInstance = await getRequest();
  const req = requestInstance.post('/graphql').send({
    query,
    variables
  });

  if (token) {
    req.set('Authorization', `Bearer ${token}`);
  }

  return await req;
}

/**
 * Send a GraphQL mutation
 * @param {string} mutation - GraphQL mutation
 * @param {Object} [variables={}] - Mutation variables
 * @param {string} [token=null] - Authentication token
 * @returns {Promise<Object>} Response
 */
async function graphqlMutation(mutation, variables = {}, token = null) {
  return await graphqlQuery(mutation, variables, token);
}

/**
 * Send a POST request to a webhook endpoint
 * @param {string} endpoint - Webhook endpoint
 * @param {Object} payload - Request payload
 * @param {Object} [headers={}] - Request headers
 * @returns {Promise<Object>} Response
 */
async function postWebhook(endpoint, payload, headers = {}) {
  const requestInstance = await getRequest();
  const req = requestInstance.post(endpoint).send(payload);

  // Set headers
  Object.entries(headers).forEach(([key, value]) => {
    req.set(key, value);
  });

  return await req;
}

/**
 * Send a WhatsApp webhook request
 * @param {Object} payload - Webhook payload
 * @param {string} signature - X-MMC-Signature header value
 * @returns {Promise<Object>} Response
 */
async function whatsappWebhook(payload, signature) {
  return await postWebhook('/whatsapp/webhook', payload, {
    'X-MMC-Signature': signature,
    'Content-Type': 'application/json'
  });
}

/**
 * Send a Stripe webhook request
 * @param {Object} payload - Webhook payload
 * @param {string} signature - Stripe-Signature header value
 * @returns {Promise<Object>} Response
 */
async function stripeWebhook(payload, signature) {
  return await postWebhook('/stripe/webhook', payload, {
    'Stripe-Signature': signature,
    'Content-Type': 'application/json'
  });
}

/**
 * Submit a cart via WhatsApp API
 * @param {Object} cartData - Cart data
 * @param {string} sessionToken - WhatsApp session token
 * @returns {Promise<Object>} Response
 */
async function submitCart(cartData, sessionToken) {
  const requestInstance = await getRequest();
  return await requestInstance
    .post('/whatsapp/submit-cart')
    .set('X-WhatsApp-Token', sessionToken)
    .set('Content-Type', 'application/json')
    .send(cartData);
}

/**
 * Create a Stripe checkout session
 * @param {Object} checkoutData - Checkout data
 * @param {string} [token=null] - Authentication token
 * @returns {Promise<Object>} Response
 */
async function createStripeCheckout(checkoutData, token = null) {
  const requestInstance = await getRequest();
  const req = requestInstance
    .post('/stripe/create-checkout-session')
    .set('Content-Type', 'application/json')
    .send(checkoutData);

  if (token) {
    req.set('Authorization', `Bearer ${token}`);
  }

  return await req;
}

module.exports = {
  initializeApp,
  cleanupApp,
  getRequest,
  graphqlQuery,
  graphqlMutation,
  postWebhook,
  whatsappWebhook,
  stripeWebhook,
  submitCart,
  createStripeCheckout
};
