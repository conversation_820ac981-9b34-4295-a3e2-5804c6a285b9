/**
 * WhatsApp webhook integration tests
 */

const { connectTestDB, clearCollections } = require('../../helpers/dbHelper');
const { clearRedis, getSession } = require('../../helpers/redisHelper');
const { generateXMMCSignature } = require('../../helpers/authHelper');
const { whatsappWebhook, cleanupApp } = require('../../helpers/apiHelper');
const { mockWhatsAppSend, cleanAllMocks } = require('../../helpers/mockHelper');
const { waitForRedisKey, delay } = require('../../helpers/waitFor');
const UserFactory = require('../../factories/userFactory');

// Mock WhatsApp service
jest.mock('../../../whatsapp/services/whatsappService');
const whatsappService = require('../../../whatsapp/services/whatsappService');

describe('WhatsApp Webhook Integration', () => {
  // Connect to test database before all tests
  beforeAll(async () => {
    await connectTestDB();
  });
  
  // Clean up before each test
  beforeEach(async () => {
    // Clear collections
    await clearCollections('User');
    
    // Clear Redis
    await clearRedis();
    
    // Reset mocks
    jest.clearAllMocks();
    cleanAllMocks();
  });
  
  // Clean up after all tests
  afterAll(async () => {
    // Clean up server
    await cleanupApp();

    // Clean up mocks
    jest.restoreAllMocks();
    cleanAllMocks();
  });
  
  test('should process incoming message and create session', async () => {
    // Create test user
    const user = await UserFactory.create({
      phone: '+1234567890'
    });
    
    // Mock WhatsApp API
    mockWhatsAppSend();
    
    // Create webhook payload
    const payload = {
      data: {
        type: 'webhook_event',
        id: 'event-123',
        attributes: {
          type: 'messages.received'
        }
      },
      included: [
        {
          type: 'messages',
          id: 'message-123',
          attributes: {
            content: [
              {
                displayType: 'text',
                attrs: {
                  text: 'Hello'
                }
              }
            ]
          }
        },
        {
          type: 'dialogues',
          id: 'dialogue-123',
          attributes: {
            status: 'ACTIVE'
          },
          relationships: {
            recipient: {
              data: {
                id: '+1234567890',
                type: 'customers'
              }
            },
            agent: {
              data: {
                id: 'brand-123',
                type: 'agents'
              }
            }
          }
        }
      ]
    };
    
    // Generate signature
    const content = JSON.stringify(payload);
    const secret = process.env.PAYEMOJI_WEBHOOK_SECRET || 'test-secret';
    const signature = generateXMMCSignature(content, secret);
    
    // Send webhook request
    const response = await whatsappWebhook(payload, signature);
    
    // Verify response
    expect(response.status).toBe(202);
    
    // Wait for session to be created
    await delay(500); // Give some time for async processing
    
    // Verify WhatsApp service was called
    expect(whatsappService.sendBasicText).toHaveBeenCalled();
    
    // Try to find session in Redis
    // This would need to be adjusted based on how sessions are stored
    try {
      const sessionKey = 'session:dialogue-123';
      const sessionData = await waitForRedisKey(
        sessionKey,
        value => value !== null,
        { timeout: 2000 }
      );
      
      // Parse session data
      const session = JSON.parse(sessionData);
      
      // Verify session data
      expect(session).toBeTruthy();
      expect(session.dialogueId).toBe('dialogue-123');
      expect(session.customerPhone).toBe('+1234567890');
      expect(session.brandWhatsappId).toBe('brand-123');
    } catch (error) {
      // If waitForRedisKey times out, fail the test
      fail(`Session was not created in Redis: ${error.message}`);
    }
  });
  
  test('should reject webhook with invalid signature', async () => {
    // Create webhook payload
    const payload = {
      data: {
        type: 'webhook_event',
        id: 'event-123',
        attributes: {
          type: 'messages.received'
        }
      },
      included: []
    };
    
    // Generate invalid signature
    const invalidSignature = 'v1=invalid-signature';
    
    // Send webhook request
    const response = await whatsappWebhook(payload, invalidSignature);
    
    // Verify response
    expect(response.status).toBe(401);
    
    // Verify WhatsApp service was not called
    expect(whatsappService.sendBasicText).not.toHaveBeenCalled();
  });
  
  test('should handle malformed webhook payload', async () => {
    // Create malformed webhook payload
    const payload = {
      data: {
        type: 'webhook_event',
        id: 'event-123',
        attributes: {
          type: 'messages.received'
        }
      }
      // Missing 'included' array
    };
    
    // Generate signature
    const content = JSON.stringify(payload);
    const secret = process.env.PAYEMOJI_WEBHOOK_SECRET || 'test-secret';
    const signature = generateXMMCSignature(content, secret);
    
    // Send webhook request
    const response = await whatsappWebhook(payload, signature);
    
    // Verify response
    // Depending on implementation, this might be 400 or 202 with error handling
    expect([400, 401, 202]).toContain(response.status);
    
    // Verify WhatsApp service was not called with message
    expect(whatsappService.sendBasicText).not.toHaveBeenCalled();
  });
});
